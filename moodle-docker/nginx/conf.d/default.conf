server {
    listen 80;
    server_name localhost;
    root /var/www/html;
    index index.php index.html index.htm;

    # Health check endpoint
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Custom splash page at root
    location = / {
        alias /var/www/frontend/;
        try_files /index.html =404;
    }

    # Serve static assets for custom frontend
    location ~ ^/(styles\.css|script\.js)$ {
        root /var/www/frontend;
        expires 1d;
        add_header Cache-Control "public";
    }

    # Main location for everything else (Moodle)
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 256 16k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
    }



    # Deny access to sensitive files
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.git {
        deny all;
    }

    # Static files optimization
    location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Video files optimization
    location ~* \.(mp4|avi|mov|wmv|flv|webm)$ {
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
        add_header Accept-Ranges bytes;
    }

    # Moodle dataroot protection
    location /moodledata {
        internal;
        alias /var/moodledata;
    }

    error_log /var/log/nginx/moodle_error.log warn;
    access_log /var/log/nginx/moodle_access.log combined;
}

server {
    listen 443 ssl;
    server_name localhost;
    root /var/www/html;
    index index.php index.html index.htm;

    ssl_certificate     /etc/nginx/certs/selfsigned.crt;
    ssl_certificate_key /etc/nginx/certs/selfsigned.key;
    ssl_protocols       TLSv1.2 TLSv1.3;
    ssl_ciphers         HIGH:!aNULL:!MD5;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Main location
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 256 16k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
    }

    # Deny access to sensitive files
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.git {
        deny all;
    }

    # Static files optimization
    location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Video files optimization
    location ~* \.(mp4|avi|mov|wmv|flv|webm)$ {
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
        add_header Accept-Ranges bytes;
    }

    # Moodle dataroot protection
    location /moodledata {
        internal;
        alias /var/moodledata;
    }

    error_log /var/log/nginx/moodle_ssl_error.log warn;
    access_log /var/log/nginx/moodle_ssl_access.log combined;
}