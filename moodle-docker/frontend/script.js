// FatbeamU Custom Frontend JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');

    // Handle form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // Handle login process
    async function handleLogin() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value;

        // Basic validation
        if (!username || !password) {
            showMessage('Please enter both username and password.', 'error');
            return;
        }

        // Show loading state
        setLoadingState(true);

        try {
            // First, get the login token from Moodle
            const tokenResponse = await fetch('/login/token.php', {
                method: 'GET',
                credentials: 'include'
            });

            let logintoken = '';
            if (tokenResponse.ok) {
                const tokenText = await tokenResponse.text();
                const tokenMatch = tokenText.match(/name="logintoken"\s+value="([^"]+)"/);
                if (tokenMatch) {
                    logintoken = tokenMatch[1];
                }
            }

            // Prepare login data
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            if (logintoken) {
                formData.append('logintoken', logintoken);
            }

            // Attempt login
            const loginResponse = await fetch('/login/index.php', {
                method: 'POST',
                body: formData,
                credentials: 'include',
                redirect: 'manual' // Don't follow redirects automatically
            });

            // Check if login was successful
            if (loginResponse.status === 303 || loginResponse.status === 302) {
                // Successful login - redirect to Moodle dashboard
                showMessage('Login successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = '/my/';
                }, 1000);
            } else {
                // Check response for error indicators
                const responseText = await loginResponse.text();

                if (responseText.includes('Invalid login') ||
                    responseText.includes('error') ||
                    responseText.includes('incorrect')) {
                    showMessage('Invalid username or password. Please try again.', 'error');
                } else {
                    // If we get here, try a different approach - direct redirect
                    window.location.href = `/login/index.php?username=${encodeURIComponent(username)}`;
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            showMessage('Login failed. Please try again or contact support.', 'error');
        } finally {
            setLoadingState(false);
        }
    }

    // Show loading state
    function setLoadingState(loading) {
        if (loading) {
            loginBtn.textContent = 'Signing In...';
            loginBtn.disabled = true;
            loginForm.classList.add('loading');
        } else {
            loginBtn.textContent = 'Sign In';
            loginBtn.disabled = false;
            loginForm.classList.remove('loading');
        }
    }

    // Show message to user
    function showMessage(text, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const message = document.createElement('div');
        message.className = `message ${type}`;
        message.textContent = text;

        // Insert before form
        loginForm.parentNode.insertBefore(message, loginForm);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 5000);
    }

    // Enhanced form validation
    function validateForm() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value;

        // Reset previous validation states
        usernameInput.classList.remove('error');
        passwordInput.classList.remove('error');

        let isValid = true;

        if (!username) {
            usernameInput.classList.add('error');
            isValid = false;
        }

        if (!password) {
            passwordInput.classList.add('error');
            isValid = false;
        }

        return isValid;
    }

    // Add real-time validation
    usernameInput.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.classList.add('error');
        } else {
            this.classList.remove('error');
        }
    });

    passwordInput.addEventListener('blur', function() {
        if (!this.value) {
            this.classList.add('error');
        } else {
            this.classList.remove('error');
        }
    });

    // Clear error states on input
    usernameInput.addEventListener('input', function() {
        this.classList.remove('error');
    });

    passwordInput.addEventListener('input', function() {
        this.classList.remove('error');
    });

    // Handle Enter key in form fields
    usernameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            passwordInput.focus();
        }
    });

    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleLogin();
        }
    });

    // Add smooth animations
    const cards = document.querySelectorAll('.feature-card, .login-card, .hero-section');

    // Intersection Observer for animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    // Initialize animation states
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Focus management
    usernameInput.focus();

    // Check if user is already logged in
    checkExistingSession();

    async function checkExistingSession() {
        try {
            const response = await fetch('/my/', {
                method: 'HEAD',
                credentials: 'include'
            });

            if (response.ok) {
                // User is already logged in
                showMessage('You are already logged in. Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = '/my/';
                }, 1500);
            }
        } catch (error) {
            // Ignore errors - user is not logged in
        }
    }
});

// Add CSS for error states
const style = document.createElement('style');
style.textContent = `
    .form-group input.error {
        border-color: #e53e3e;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .form-group input.error:focus {
        border-color: #e53e3e;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2);
    }
`;
document.head.appendChild(style);
