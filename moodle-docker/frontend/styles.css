/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.logo h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.tagline {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Hero Section */
.hero-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.hero-section h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2d3748;
}

.hero-description {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 40px;
    line-height: 1.7;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.feature-card {
    background: #f7fafc;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2d3748;
}

.feature-card p {
    color: #4a5568;
    font-size: 0.9rem;
}

/* Login Section */
.login-section {
    display: flex;
    align-items: flex-start;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    width: 100%;
}

.login-card h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2d3748;
    text-align: center;
}

.login-card > p {
    color: #4a5568;
    text-align: center;
    margin-bottom: 30px;
}

/* Form Styles */
.login-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2d3748;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

/* Login Links */
.login-links {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.login-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.login-links a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Footer */
.footer {
    margin-top: 40px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    padding: 20px 0;
}

.footer-links {
    margin-top: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    margin: 0 15px;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .logo h1 {
        font-size: 2.5rem;
    }
    
    .hero-section h2 {
        font-size: 2rem;
    }
    
    .hero-section,
    .login-card {
        padding: 30px;
    }
    
    .footer-links a {
        display: block;
        margin: 5px 0;
    }
}

/* Loading Animation */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading .login-btn {
    background: #a0aec0;
    cursor: not-allowed;
}

/* Success/Error Messages */
.message {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
}

.message.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
}
