<?php  // Moodle configuration file

unset($CFG);
global $CFG;
$CFG = new stdClass();

$CFG->dbtype    = 'mysqli';
$CFG->dblibrary = 'native';
$CFG->dbhost    = getenv('MOODLE_DB_HOST') ?: 'mysql';
$CFG->dbname    = getenv('MOODLE_DB_NAME') ?: 'moodle';
$CFG->dbuser    = getenv('MOODLE_DB_USER') ?: 'moodle';
$CFG->dbpass    = getenv('MOODLE_DB_PASS') ?: '';
$CFG->prefix    = getenv('MOODLE_DB_PREFIX') ?: 'mdl_';
$CFG->dboptions = array (
  'dbpersist' => 0,
  'dbport' => getenv('MOODLE_DB_PORT') ?: 3306,
  'dbsocket' => '',
  'dbcollation' => 'utf8mb4_unicode_ci',
);

$CFG->wwwroot   = getenv('MOODLE_WWWROOT') ?: 'http://localhost';
$CFG->dataroot  = getenv('MOODLE_DATAROOT') ?: '/var/moodledata';
$CFG->admin     = getenv('MOODLE_ADMIN') ?: 'admin';

$CFG->directorypermissions = 0777;

// Session configuration with Redis
$CFG->sessiontimeout = getenv('MOODLE_SESSION_TIMEOUT') ?: 28800; // 8 hours in seconds
$CFG->sessiontimeoutwarning = getenv('MOODLE_SESSION_WARNING') ?: 1200; // 20 minutes warning
$CFG->sessioncookie = getenv('MOODLE_SESSION_COOKIE') ?: 'MoodleSession';
$CFG->sessioncookiepath = '/';
$CFG->sessioncookiedomain = '';
$CFG->sessioncookiesecure = false; // Set to true in production with HTTPS
$CFG->sessioncookiehttponly = true;
$CFG->sessioncookiesamesite = 'Lax';

// Redis session handler configuration (re-enabled)
$CFG->session_handler_class = '\core\session\redis';
$CFG->session_redis_host = getenv('MOODLE_REDIS_HOST') ?: 'redis';
$CFG->session_redis_port = getenv('MOODLE_REDIS_PORT') ?: 6379;
$CFG->session_redis_database = 0;
$CFG->session_redis_auth = '';
$CFG->session_redis_prefix = 'moodle_sess_';
$CFG->session_redis_acquire_lock_timeout = 120;
$CFG->session_redis_lock_expire = 7200;

// Performance and debugging
$CFG->cachejs = false;
$CFG->yuicomboloading = false;

// Debugging and logging settings (enabled for monitoring)
$CFG->debug = 15; // DEBUG_NORMAL - logs errors without displaying them
$CFG->debugdisplay = 0; // Don't display errors on screen
$CFG->debugsmtp = 1; // Enable SMTP debugging
$CFG->debugstringids = 0; // Don't show string IDs
$CFG->debugvalidators = 0; // Don't show validator links
$CFG->debugpageinfo = 0; // Don't show page info

// Enhanced logging configuration
$CFG->log_manager_class = '\core\log\manager';
$CFG->logstore_standard_log_guest = true; // Log guest actions
$CFG->logstore_standard_log_lifetime = 365; // Keep logs for 1 year

// Additional security and performance settings
$CFG->passwordsaltmain = 'your-random-salt-here-change-this-in-production';
$CFG->preventexecpath = true;
$CFG->disableupdateautodeploy = true;

require_once(__DIR__ . '/lib/setup.php');

// All sensitive/environment-specific values are now loaded from environment variables.
// Set these in your .env file or Docker secrets. Do not hardcode secrets in this file.
// There is no php closing tag in this file,
// it is intentional because it prevents trailing whitespace problems!
